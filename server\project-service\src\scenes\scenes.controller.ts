/**
 * 场景控制器
 */
import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MessagePattern } from '@nestjs/microservices';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ScenesService } from './scenes.service';
import { CreateSceneDto } from './dto/create-scene.dto';
import { UpdateSceneDto } from './dto/update-scene.dto';
import { CreateSceneEntityDto } from './dto/create-scene-entity.dto';
import { UpdateSceneEntityDto } from './dto/update-scene-entity.dto';
import { Scene } from './entities/scene.entity';
import { SceneEntity } from './entities/scene-entity.entity';

@ApiTags('场景')
@Controller('projects/:projectId/scenes')
export class ScenesController {
  constructor(private readonly scenesService: ScenesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建场景' })
  @ApiResponse({ status: 201, description: '场景创建成功', type: Scene })
  async create(
    @Param('projectId') projectId: string,
    @Request() req,
    @Body() createSceneDto: CreateSceneDto,
  ): Promise<Scene> {
    return this.scenesService.create(projectId, req.user.id, createSceneDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取项目的所有场景' })
  @ApiResponse({ status: 200, description: '返回项目的所有场景', type: [Scene] })
  async findAll(@Param('projectId') projectId: string, @Request() req): Promise<Scene[]> {
    return this.scenesService.findAll(projectId, req.user.id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取场景' })
  @ApiResponse({ status: 200, description: '返回场景信息', type: Scene })
  async findOne(@Param('id') id: string, @Request() req): Promise<Scene> {
    return this.scenesService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新场景' })
  @ApiResponse({ status: 200, description: '场景更新成功', type: Scene })
  async update(@Param('id') id: string, @Request() req, @Body() updateSceneDto: UpdateSceneDto): Promise<Scene> {
    return this.scenesService.update(id, req.user.id, updateSceneDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除场景' })
  @ApiResponse({ status: 204, description: '场景删除成功' })
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    return this.scenesService.remove(id, req.user.id);
  }

  @Post(':id/entities')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建场景实体' })
  @ApiResponse({ status: 201, description: '场景实体创建成功', type: SceneEntity })
  async createEntity(
    @Param('id') id: string,
    @Request() req,
    @Body() createEntityDto: CreateSceneEntityDto,
  ): Promise<SceneEntity> {
    return this.scenesService.createEntity(id, req.user.id, createEntityDto);
  }

  @Get(':id/entities')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取场景的所有实体' })
  @ApiResponse({ status: 200, description: '返回场景的所有实体', type: [SceneEntity] })
  async findAllEntities(@Param('id') id: string, @Request() req): Promise<SceneEntity[]> {
    return this.scenesService.findAllEntities(id, req.user.id);
  }

  @Get(':sceneId/entities/:entityId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取场景实体' })
  @ApiResponse({ status: 200, description: '返回场景实体信息', type: SceneEntity })
  async findOneEntity(@Param('entityId') entityId: string, @Request() req): Promise<SceneEntity> {
    return this.scenesService.findOneEntity(entityId, req.user.id);
  }

  @Patch(':sceneId/entities/:entityId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新场景实体' })
  @ApiResponse({ status: 200, description: '场景实体更新成功', type: SceneEntity })
  async updateEntity(
    @Param('entityId') entityId: string,
    @Request() req,
    @Body() updateEntityDto: UpdateSceneEntityDto,
  ): Promise<SceneEntity> {
    return this.scenesService.updateEntity(entityId, req.user.id, updateEntityDto);
  }

  @Delete(':sceneId/entities/:entityId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除场景实体' })
  @ApiResponse({ status: 204, description: '场景实体删除成功' })
  async removeEntity(@Param('entityId') entityId: string, @Request() req): Promise<void> {
    return this.scenesService.removeEntity(entityId, req.user.id);
  }

  // 微服务消息处理
  @MessagePattern({ cmd: 'createScene' })
  async handleCreateScene(data: { projectId: string; userId: string } & CreateSceneDto): Promise<Scene> {
    const { projectId, userId, ...createSceneDto } = data;
    return this.scenesService.create(projectId, userId, createSceneDto as CreateSceneDto);
  }

  @MessagePattern({ cmd: 'findAllScenes' })
  async handleFindAllScenes(data: { projectId: string; userId: string }): Promise<Scene[]> {
    return this.scenesService.findAll(data.projectId, data.userId);
  }

  @MessagePattern({ cmd: 'findSceneById' })
  async handleFindSceneById(data: { id: string; userId: string }): Promise<Scene> {
    return this.scenesService.findOne(data.id, data.userId);
  }

  @MessagePattern({ cmd: 'updateScene' })
  async handleUpdateScene(data: { id: string; userId: string } & UpdateSceneDto): Promise<Scene> {
    const { id, userId, ...updateSceneDto } = data;
    return this.scenesService.update(id, userId, updateSceneDto as UpdateSceneDto);
  }

  @MessagePattern({ cmd: 'deleteScene' })
  async handleDeleteScene(data: { id: string; userId: string }): Promise<void> {
    return this.scenesService.remove(data.id, data.userId);
  }

  @MessagePattern({ cmd: 'createSceneEntity' })
  async handleCreateSceneEntity(data: { sceneId: string; userId: string } & CreateSceneEntityDto): Promise<SceneEntity> {
    const { sceneId, userId, ...createEntityDto } = data;
    return this.scenesService.createEntity(sceneId, userId, createEntityDto as CreateSceneEntityDto);
  }

  @MessagePattern({ cmd: 'findAllSceneEntities' })
  async handleFindAllSceneEntities(data: { sceneId: string; userId: string }): Promise<SceneEntity[]> {
    return this.scenesService.findAllEntities(data.sceneId, data.userId);
  }

  @MessagePattern({ cmd: 'findSceneEntityById' })
  async handleFindSceneEntityById(data: { entityId: string; userId: string }): Promise<SceneEntity> {
    return this.scenesService.findOneEntity(data.entityId, data.userId);
  }

  @MessagePattern({ cmd: 'updateSceneEntity' })
  async handleUpdateSceneEntity(data: { entityId: string; userId: string } & UpdateSceneEntityDto): Promise<SceneEntity> {
    const { entityId, userId, ...updateEntityDto } = data;
    return this.scenesService.updateEntity(entityId, userId, updateEntityDto as UpdateSceneEntityDto);
  }

  @MessagePattern({ cmd: 'deleteSceneEntity' })
  async handleDeleteSceneEntity(data: { entityId: string; userId: string }): Promise<void> {
    return this.scenesService.removeEntity(data.entityId, data.userId);
  }
}
