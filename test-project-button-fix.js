#!/usr/bin/env node
/**
 * 项目创建按钮修复验证脚本
 * 验证创建新项目按钮在各种情况下都能正确显示
 */

const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查计数器
let totalChecks = 0;
let passedChecks = 0;

function checkFileExists(filePath, description) {
  totalChecks++;
  const fullPath = path.resolve(filePath);
  
  if (fs.existsSync(fullPath)) {
    colorLog('green', `✅ ${description}: 文件存在`);
    passedChecks++;
    return true;
  } else {
    colorLog('red', `❌ ${description}: 文件不存在 - ${filePath}`);
    return false;
  }
}

function checkFileContent(filePath, pattern, description) {
  totalChecks++;
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
    
    if (regex.test(content)) {
      colorLog('green', `✅ ${description}: 内容检查通过`);
      passedChecks++;
      return true;
    } else {
      colorLog('red', `❌ ${description}: 内容检查失败`);
      return false;
    }
  } catch (error) {
    colorLog('red', `❌ ${description}: 读取文件失败 - ${error.message}`);
    return false;
  }
}

// 开始验证
colorLog('cyan', '🔍 开始验证项目创建按钮修复...\n');

console.log('1. 验证前端页面修复...');

// 检查项目页面文件
checkFileExists('editor/src/pages/ProjectsPage.tsx', '项目页面文件');

// 检查创建项目逻辑修复
checkFileContent(
  'editor/src/pages/ProjectsPage.tsx',
  /dispatch\(fetchProjects\(\)\)/,
  '项目创建成功后刷新列表逻辑'
);

// 检查网格视图创建卡片
checkFileContent(
  'editor/src/pages/ProjectsPage.tsx',
  /isCreateCard.*true/,
  '网格视图创建新项目卡片'
);

// 检查列表视图创建项
checkFileContent(
  'editor/src/pages/ProjectsPage.tsx',
  /创建新项目的特殊项目/,
  '列表视图创建新项目项'
);

// 检查渲染逻辑修复
checkFileContent(
  'editor/src/pages/ProjectsPage.tsx',
  /始终显示项目列表/,
  '项目列表渲染逻辑修复'
);

console.log('\n2. 验证翻译文件修复...');

// 检查中文翻译文件
checkFileExists('editor/src/i18n/locales/zh-CN.json', '中文翻译文件');

// 检查新增翻译键
checkFileContent(
  'editor/src/i18n/locales/zh-CN.json',
  /"createNew":\s*"创建新项目"/,
  '创建新项目翻译键'
);

checkFileContent(
  'editor/src/i18n/locales/zh-CN.json',
  /"createNewDescription":\s*"点击创建一个新的项目"/,
  '创建新项目描述翻译键'
);

// 检查英文翻译文件
checkFileExists('editor/src/i18n/locales/en-US.json', '英文翻译文件');

checkFileContent(
  'editor/src/i18n/locales/en-US.json',
  /"createNew":\s*"Create New Project"/,
  '英文创建新项目翻译键'
);

console.log('\n3. 验证配置文件一致性...');

// 检查环境配置文件
checkFileExists('.env', '环境配置文件');

// 检查Docker Compose配置
checkFileExists('docker-compose.windows.yml', 'Docker Compose配置文件');

// 检查启动脚本
checkFileExists('start-windows.ps1', 'Windows启动脚本');
checkFileExists('stop-windows.ps1', 'Windows停止脚本');

// 检查前端Dockerfile
checkFileExists('editor/Dockerfile', '前端Dockerfile');

// 检查项目服务Dockerfile
checkFileExists('server/project-service/Dockerfile', '项目服务Dockerfile');

console.log('\n4. 验证项目服务配置...');

// 检查项目服务环境变量配置
checkFileContent(
  'docker-compose.windows.yml',
  /DB_DATABASE_PROJECTS=dl_engine_projects/,
  '项目服务数据库配置'
);

// 检查项目服务端口配置
checkFileContent(
  'docker-compose.windows.yml',
  /PROJECT_SERVICE_PORT=3002/,
  '项目服务端口配置'
);

console.log('\n5. 验证前端配置...');

// 检查前端API配置
checkFileContent(
  'docker-compose.windows.yml',
  /REACT_APP_API_URL=\/api/,
  '前端API配置'
);

// 检查前端协作服务配置
checkFileContent(
  'docker-compose.windows.yml',
  /REACT_APP_COLLABORATION_SERVER_URL=\/ws/,
  '前端协作服务配置'
);

// 输出结果
console.log('\n' + '='.repeat(60));
colorLog('cyan', '📊 验证结果统计:');
console.log(`✅ 通过: ${passedChecks}项检查`);
console.log(`❌ 失败: ${totalChecks - passedChecks}项检查`);
console.log(`📈 成功率: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);

if (passedChecks === totalChecks) {
  colorLog('green', '\n🎉 所有检查都通过了！项目创建按钮修复验证成功！');
  console.log('\n📋 修复内容总结:');
  console.log('1. ✅ 修复了项目创建成功后按钮消失的问题');
  console.log('2. ✅ 在网格视图和列表视图中都添加了创建新项目的选项');
  console.log('3. ✅ 优化了项目列表的渲染逻辑');
  console.log('4. ✅ 添加了必要的翻译键');
  console.log('5. ✅ 确保了配置文件的一致性');
  
  console.log('\n🚀 部署建议:');
  console.log('1. 重新构建前端服务: docker-compose -f docker-compose.windows.yml build editor');
  console.log('2. 重启服务: .\\stop-windows.ps1 && .\\start-windows.ps1');
  console.log('3. 访问 http://localhost 验证修复效果');
} else {
  colorLog('red', '\n⚠️  部分检查未通过，请检查上述失败项目并修复。');
}

console.log('\n' + '='.repeat(60));
