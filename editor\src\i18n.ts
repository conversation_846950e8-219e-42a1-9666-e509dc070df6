/**
 * 国际化配置
 */
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// 导入主要翻译文件
import zhCNTranslations from './i18n/locales/zh-CN.json';
import enUSTranslations from './i18n/locales/en-US.json';

// 导入成就相关翻译
import achievementsZH from './locales/zh-CN/achievements.json';
import achievementsEN from './locales/en/achievements.json';

// 导入教程相关翻译
import tutorialsZH from './locales/zh-CN/tutorials.json';
import tutorialsEN from './locales/en/tutorials.json';

// 导入帮助相关翻译
import helpZH from './locales/zh-CN/help.json';

// 资源
const resources = {
  'en-US': {
    translation: {
      ...enUSTranslations,
      achievements: achievementsEN.achievements,
      tutorials: tutorialsEN.tutorials
    },
    auth: enUSTranslations.auth
  },
  'zh-CN': {
    translation: {
      ...zhCNTranslations,
      achievements: achievementsZH.achievements,
      tutorials: tutorialsZH.tutorials,
      help: helpZH.help
    },
    auth: zhCNTranslations.auth
  }
};

i18n
  // 将 i18n 实例传递给 react-i18next
  .use(initReactI18next)
  // 初始化 i18next
  .init({
    resources,
    lng: 'zh-CN', // 强制设置语言为中文
    fallbackLng: 'zh-CN', // 默认语言
    ns: ['translation', 'auth'],
    defaultNS: 'translation',
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false // 不转义 HTML
    },
    react: {
      useSuspense: false
    },
    // 添加缺失键处理
    missingKeyHandler: (lng, ns, key, fallbackValue) => {
      console.warn(`Missing translation key: ${ns}:${key} for language: ${lng}`);
      return fallbackValue || key;
    },
    // 确保翻译键正确解析
    keySeparator: '.',
    nsSeparator: ':',
    returnEmptyString: false,
    returnNull: false
  })
  .then(() => {
    console.log('i18n initialized successfully');
    console.log('Current language:', i18n.language);
    console.log('Available resources:', Object.keys(i18n.store.data));
    console.log('Test translation auth:loginTitle:', i18n.t('loginTitle', { ns: 'auth' }));
  })
  .catch((error) => {
    console.error('i18n initialization failed:', error);
  });

export default i18n;
